import os
import pandas as pd

import xml.etree.ElementTree as ET
import csv
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import pydicom
import cv2 
from skimage import exposure, filters, morphology 
import numpy as np
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.models import Sequential
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
from tqdm import tqdm
from PIL import Image
from tensorflow.keras.applications import DenseNet121
from tensorflow.keras.applications import EfficientNetB4
from tensorflow.keras.applications import EfficientNetB1
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau



# Chemin de base vers tes fichiers CSV
base_path = r'C:\Users\<USER>\pulmoscan\pulmo\PulmoScan\Data\classification'

# Charger les fichiers CSV
annotations_combine = pd.read_csv(os.path.join(base_path, 'annotations_combine.csv'))
classification_nodules = pd.read_csv(os.path.join(base_path, 'classification_nodules.csv'))
df_classification = pd.read_csv(os.path.join(base_path, 'df_classification.csv'))
df_filtered = pd.read_csv(os.path.join(base_path, 'df_filtred.csv'))


'''

# Chemin du dossier contenant les fichiers XML
chemin_dossier_xml = r'C:\Users\<USER>\pulmoscan\pulmo\PulmoScan\Data\tcia-lidc-xml\189'

# Nom du fichier CSV de sortie
fichier_csv = 'annotations5.csv'

# Namespace
namespace = {'ns': 'http://www.nih.gov'}

# Variable pour stocker les en-têtes
en_tetes = None

# Ouvrir le fichier CSV en mode écriture
with open(fichier_csv, mode='w', newline='', encoding='utf-8') as csv_file:
    writer = csv.writer(csv_file)
    
    # Parcourir chaque fichier dans le dossier
    for fichier in os.listdir(chemin_dossier_xml):
        if fichier.endswith('.xml'):
            chemin_fichier = os.path.join(chemin_dossier_xml, fichier)
            
            # Parser le fichier XML
            tree = ET.parse(chemin_fichier)
            root = tree.getroot()
            
            # Si les en-têtes n'ont pas encore été définis, les extraire du premier fichier XML
            if en_tetes is None:
                en_tetes = [
                    'annotationVersion', 'servicingRadiologistID',
                    'noduleID', 'imageZposition', 'imageSOP_UID', 'inclusion',
                    'subtlety', 'internalStructure', 'calcification', 'sphericity',
                    'margin', 'lobulation', 'spiculation', 'texture', 'malignancy',
                    'edgeMap_xCoord', 'edgeMap_yCoord'
                ]
                writer.writerow(en_tetes)
            
            # Extraire les données nécessaires
            for reading_session in root.findall('ns:readingSession', namespace):
                annotation_version = reading_session.find('ns:annotationVersion', namespace).text
                servicing_radiologist_id = reading_session.find('ns:servicingRadiologistID', namespace).text
                
                for nodule in reading_session.findall('ns:unblindedReadNodule', namespace):
                    nodule_id = nodule.find('ns:noduleID', namespace).text
                    
                    # Extraire les caractéristiques du nodule
                    characteristics = nodule.find('ns:characteristics', namespace)
                    if characteristics is not None:
                        subtlety = characteristics.find('ns:subtlety', namespace).text
                        internal_structure = characteristics.find('ns:internalStructure', namespace).text
                        calcification = characteristics.find('ns:calcification', namespace).text
                        sphericity = characteristics.find('ns:sphericity', namespace).text
                        margin = characteristics.find('ns:margin', namespace).text
                        lobulation = characteristics.find('ns:lobulation', namespace).text
                        spiculation = characteristics.find('ns:spiculation', namespace).text
                        texture = characteristics.find('ns:texture', namespace).text
                        malignancy = characteristics.find('ns:malignancy', namespace).text
                    else:
                        subtlety = internal_structure = calcification = sphericity = margin = lobulation = spiculation = texture = malignancy = ''
                    
                    # Extraire les informations du ROI
                    for roi in nodule.findall('ns:roi', namespace):
                        image_zposition = roi.find('ns:imageZposition', namespace).text
                        image_sop_uid = roi.find('ns:imageSOP_UID', namespace).text
                        inclusion = roi.find('ns:inclusion', namespace).text
                        
                        # Extraire les coordonnées edgeMap
                        for edge_map in roi.findall('ns:edgeMap', namespace):
                            x_coord = edge_map.find('ns:xCoord', namespace).text
                            y_coord = edge_map.find('ns:yCoord', namespace).text
                            
                            # Écrire une ligne dans le CSV
                            ligne = [
                                annotation_version, servicing_radiologist_id,
                                nodule_id, image_zposition, image_sop_uid, inclusion,
                                subtlety, internal_structure, calcification, sphericity,
                                margin, lobulation, spiculation, texture, malignancy,
                                x_coord, y_coord
                            ]
                            writer.writerow(ligne)

print(f"Les données ont été écrites dans {fichier_csv}")

'''

"""

# Liste des fichiers à combiner
fichiers = ['annotations.csv','annotations1.csv' ,'annotations2.csv', 'annotations3.csv', 'annotations4.csv', 'annotations5.csv']

# Lire et combiner les fichiers
dataframes = [pd.read_csv(fichier) for fichier in fichiers]
df_combine = pd.concat(dataframes, ignore_index=True)

# Sauvegarder le fichier combiné
df_combine.to_csv('annotations_combine.csv', index=False)

"""

## data = pd.read_csv('annotations_combine.csv')
data = annotations_combine

"""

def basic_statistics(data):
    print("Infos:\n", data.info())
    print("Description:\n", data.describe())
    print("Presence of null values:\n")
    print(data.isnull().sum())

"""

"""

if __name__ == "__main__":
   
    basic_statistics(data)
    
    print("Data:\n", data.head())

"""

## data.head(5)

"""

columns_to_drop = ["servicingRadiologistID", "annotationVersion"]
df_filtred = data.drop(columns=columns_to_drop, errors='ignore')  # Ignore si la colonne n'existe pas

# Sauvegarde du fichier nettoyé
cleaned_file_path = "df_filtred.csv"
df_filtred .to_csv(cleaned_file_path, index=False)

print(f"Fichier nettoyé enregistré sous : {cleaned_file_path}")

"""

# Fonction pour visualiser la distribution des nodules concereuses et non concereuses
def plot_distribution(data, column):
    """Affiche un histogramme de la colonne spécifiée"""
    if column in data.columns:
        plt.figure(figsize=(8, 5))
        sns.histplot(data[column].dropna(), bins=30, kde=True)
        plt.xlabel(column)
        plt.ylabel("Nombre d'observations")
        plt.title(f"Distribution de {column}")
        plt.show()
    else:
        print(f"⚠️ La colonne '{column}' n'existe pas dans les données.")

if __name__ == "__main__":
    plot_distribution(df_filtered, "malignancy")
    

malignancy_col = "malignancy"


if malignancy_col in df_filtered.columns:
    
    
    
    # Création de la colonne 'label' avec gestion des NaN
    def classify_malignancy(value):
        if pd.isna(value):  # Si NaN, ne pas classifier
            return None
        return 0 if value in [1, 2] else 1  # 0 = Bénin, 1 = Malin

    df_filtered["label"] = df_filtered[malignancy_col].apply(classify_malignancy)

    # Exclure les valeurs None pour les visualisations
    df_classification = df_filtered.dropna(subset=["label"]).copy()
    df_classification["label"] = df_classification["label"].astype(int)  # Convertir en entier après suppression des NaN

    
    
    # 1️⃣ Histogramme de la répartition des nodules
    plt.figure(figsize=(6, 4))
    sns.countplot(x=df_classification["label"], palette=["green", "red"])
    plt.xticks(ticks=[0, 1], labels=["Bénin", "Malin"])
    plt.xlabel("Type de Nodule")
    plt.ylabel("Nombre d'Occurrences")
    plt.title("Répartition des Nodules Malins et Bénins")
    plt.show()

    # Boxplot de la distribution des nodules bénins et malins en fonction de 'internal_structure'
    plt.figure(figsize=(10, 6))

    sns.boxplot(x="label", y="sphericity", data=df_classification, palette=[ "red","green"])

    plt.xlabel("Type de Nodule (0=Bénin, 1=Malin)")
    plt.ylabel("sphericity")
    plt.title("Répartition des Nodules (Bénins et Malins) en fonction de la sphericity")
    plt.xticks([0, 1], ["Malin","Bénin"])  # Pour remplacer les valeurs 0 et 1 par 'Bénin' et 'Malin'
    plt.show()
else:
    print("⚠️ La colonne 'malignancy' est absente du dataset.")

df_classification.head(5)

"""

# Sauvegarde du fichier nettoyé
file_path = "df_classification.csv"
df_classification .to_csv(file_path, index=False)

print(f"Fichier  enregistré sous : {file_path}")

"""

# 2. Séparer les malins (label 1) et bénins (label 0)
malins = df_classification[df_classification['label'] == 1] 
benins = df_classification[df_classification['label'] == 0] 

# 3. Choisir la taille de l'échantillon malins (ex: même nombre que les benins)
#n_echantillon_benin = len(malins)  # Option 1: même quantité
pourcentage = 0.4  # 40% des malins
malins_echantillon = malins.sample(frac=pourcentage, random_state=42)

# 5. Combiner les données
df_final_retouches = pd.concat([benins, malins_echantillon])

# 6. Mélanger les lignes
df_final_retouches = df_final_retouches.sample(frac=1, random_state=42).reset_index(drop=True)

# 7. Sauvegarder le nouveau CSV
df_final_retouches.to_csv('classification_nodules_retouches.csv', index=False)

print(f"Dataset final créé avec:")
print(f"- {len(benins)} nodules benins (100%)")
print(f"- {len(malins_echantillon)} nodules malins (échantillon)")
print(f"Total: {len(df_final_retouches)} lignes")

# Fonction pour normaliser l'image
def normalize_image(image):
    """Normalise l'image pour améliorer le contraste."""
    return exposure.rescale_intensity(image, in_range='image', out_range=(0, 255))


# Fonction pour extraire les poumons
def extract_lungs(image):
    """Extrait les poumons en utilisant un seuillage et des opérations morphologiques."""
    # Appliquer un seuillage pour isoler les poumons
    threshold = filters.threshold_otsu(image)
    binary_image = image > threshold

    # Supprimer les petits objets (artefacts)
    cleaned_image = morphology.remove_small_objects(binary_image, min_size=500)

    # Remplir les trous dans les poumons
    filled_image = morphology.binary_closing(cleaned_image, morphology.disk(5))

    # Appliquer le masque à l'image originale
    lungs_image = np.where(filled_image, image, 0)
    return lungs_image


def find_all_dicom_files(root_dir):
    """Trouve tous les fichiers DICOM dans l'arborescence"""
    dicom_files = []
    
    for root, dirs, files in tqdm(os.walk(root_dir), desc="Scanning DICOM files"):
        for file in files:
            if file.endswith('.dcm') or file.endswith('.DCM'):
                full_path = os.path.join(root, file)
                dicom_files.append(full_path)
    
    return dicom_files

def load_and_display_lungs(dicom_files):
    """Charge et affiche les images DICOM à partir d'une liste de chemins"""
    for dicom_path in tqdm(dicom_files, desc="Processing DICOM files"):
        try:
            # Charger le fichier DICOM
            dicom_image = pydicom.dcmread(dicom_path)
            image_data = dicom_image.pixel_array

            # Normaliser l'image
            normalized_image = normalize_image(image_data)

            # Extraire les poumons
            lungs_image = extract_lungs(normalized_image)

            # Afficher l'image originale et les poumons extraits
            plt.figure(figsize=(12, 6))

            # Image originale
            plt.subplot(1, 2, 1)
            plt.imshow(image_data, cmap=plt.cm.bone)
            plt.title(f"Image originale : {os.path.basename(dicom_path)}")
            plt.axis('off')

            # Poumons extraits
            plt.subplot(1, 2, 2)
            plt.imshow(lungs_image, cmap=plt.cm.bone)
            plt.title(f"Poumons extraits : {os.path.basename(dicom_path)}")
            plt.axis('off')

            plt.show()
            
        except Exception as e:
            print(f"Erreur lors de la lecture du fichier {dicom_path}: {e}")
            continue


# Utilisation
## dicom_root = r'C:\Users\<USER>\pulmoscan\pulmo\PulmoScan\Data\manifest-1600709154662\LIDC-IDRI'
dicom_root = r'D:\Data\manifest-1600709154662\LIDC-IDRI'
dicom_files = find_all_dicom_files(dicom_root)

print(f"Nombre total de fichiers DICOM trouvés: {len(dicom_files)}")

# Afficher seulement les 5 premiers fichiers pour le test
load_and_display_lungs(dicom_files[:5])

# Chemin de base vers le fichier metadata
base_path = r'D:\Data\manifest-1600709154662'

# Charger les fichiers CSV
df_metadata = pd.read_csv(os.path.join(base_path, 'metadata.csv'))
df_metadata.head(5)


# ==================== CONFIGURATION ====================
classifcation_base_path = r'C:\Users\<USER>\pulmoscan\pulmo\PulmoScan\Data\classification'
base_path = r'D:\Data\manifest-1600709154662'
dicom_root = os.path.join(base_path, 'LIDC-IDRI')
output_images_dir = os.path.join(base_path, 'extracted_images')
os.makedirs(output_images_dir, exist_ok=True)

# ==================== CHARGEMENT DES DONNEES ====================
df_nodule = pd.read_csv(os.path.join(classifcation_base_path, 'classification_nodules_retouches.csv'))
df_nodule['imageSOP_UID'] = df_nodule['imageSOP_UID'].astype(str)
sop_uids_set = set(df_nodule['imageSOP_UID'])

# ==================== FONCTION DE VERIFICATION UID ====================
def verify_uid_matching():
    #Vérifie la correspondance entre les UID DICOM et ceux du CSV
    sample_dcm = None
    # Trouver un fichier DICOM valide
    for root, _, files in os.walk(dicom_root):
        for file in files:
            if file.lower().endswith('.dcm'):
                sample_dcm = os.path.join(root, file)
                break
        if sample_dcm:
            break
    
    if sample_dcm:
        ds = pydicom.dcmread(sample_dcm)
        print("\n🔍 Comparaison des UID:")
        print(f"Exemple d'UID dans un DICOM: {ds.SOPInstanceUID}")
        print(f"Exemple d'UID dans classification_nodules_retouches.csv: {next(iter(sop_uids_set))}")
    else:
        print("Aucun fichier DICOM trouvé pour la comparaison")

verify_uid_matching()

# ==================== SOLUTION COMPLETE ====================
# 1. Trouver tous les fichiers DICOM
dcm_files = []
for root, _, files in os.walk(dicom_root):
    for file in files:
        if file.lower().endswith('.dcm'):
            dcm_files.append(os.path.join(root, file))

# 2. Traiter chaque fichier
image_paths = []
labels = []
uid_mismatch_count = 0

for dcm_file in tqdm(dcm_files, desc="Traitement DICOM"):
    try:
        ds = pydicom.dcmread(dcm_file)
        sop_uid = getattr(ds, 'SOPInstanceUID', '')
        
        if sop_uid in sop_uids_set:
            # Conversion de l'image
            img_array = ds.pixel_array
            img_norm = ((img_array - img_array.min()) / (img_array.max() - img_array.min()) * 255).astype(np.uint8)
            img = Image.fromarray(img_norm)
            
            # Sauvegarde
            save_path = os.path.join(output_images_dir, f"{sop_uid}.png")
            img.save(save_path)
            
            image_paths.append(save_path)
            labels.append(df_nodule[df_nodule['imageSOP_UID'] == sop_uid]['label'].values[0])
        else:
            uid_mismatch_count += 1
            
    except Exception as e:
        continue

# ==================== RESULTATS ====================
if image_paths:
    df_images = pd.DataFrame({'filename': image_paths, 'label': labels})
    df_images['label'] = df_images['label'].map({0: 'benin', 1: 'malin'})
    
    print(f"\n✅ {len(image_paths)} images converties avec succès!")
    print(f"⚠️ {uid_mismatch_count} fichiers avec UID non correspondants")
    print("Répartition des classes:")
    print(df_images['label'].value_counts())
    
    # Sauvegarde des résultats
    df_images.to_csv(os.path.join(base_path, 'image_labels.csv'), index=False)
    print(f"\n📁 Résultats sauvegardés dans {os.path.join(base_path, 'image_labels.csv')}")
else:
    print("\n❌ Aucune correspondance trouvée. Problèmes possibles:")
    print("1. Les UID dans classification_nodules.csv ne correspondent pas aux fichiers DICOM")
    print("2. Le fichier classification_nodules.csv peut être associé à un autre jeu de données")
    print("3. Les fichiers DICOM attendus ne sont pas dans le dossier spécifié")

    # Afficher des exemples pour comparaison
    if dcm_files:
        ds = pydicom.dcmread(dcm_files[0])
        print(f"\nExemple d'UID DICOM: {ds.SOPInstanceUID}")
        print(f"Exemple d'UID attendu: {next(iter(sop_uids_set))}")



# Chemin vers le fichier CSV sauvegardé durant la phase de conversion
base_path = r'D:\Data\manifest-1600709154662'
csv_path = os.path.join(base_path, 'image_labels.csv')

# Charger le DataFrame
df_images = pd.read_csv(csv_path)

# Afficher un aperçu
print("Aperçu du DataFrame:")
print(df_images.head())

# Créer l'objet ImageDataGenerator avec validation_split
datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.2,  # 20% des données pour la validation
    rotation_range=15,
    width_shift_range=0.1,
    height_shift_range=0.1,
    zoom_range=0.1,
    horizontal_flip=True
)

# Générateur pour l'entraînement
train_gen = datagen.flow_from_dataframe(
    dataframe=df_images,
    x_col='filename',       # colonne contenant les chemins d'images
    y_col='label',          # colonne contenant les labels
    target_size=(224, 224), # ajustez la taille selon vos besoins
    batch_size=32,
    class_mode='binary',
    subset='training',
    directory=None          # les chemins dans 'filename' sont absolus
)

# Générateur pour la validation
val_gen = datagen.flow_from_dataframe(
    dataframe=df_images,
    x_col='filename',
    y_col='label',
    target_size=(224, 224),
    batch_size=32,
    class_mode='binary',
    subset='validation',
    directory=None
)

# Afficher le nombre d'images dans chaque générateur
print(f"Nombre d'images d'entraînement : {train_gen.samples}")
print(f"Nombre d'images de validation   : {val_gen.samples}")




# Charger DenseNet121 sans la dernière couche (include_top=False)
base_model = DenseNet121(weights='imagenet', include_top=False, input_shape=(224, 224, 3))

# Geler les couches de base (optionnel pour le fine-tuning)
for layer in base_model.layers:
    layer.trainable = False

# Ajouter des couches pour classification binaire
x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.5)(x)
predictions = Dense(1, activation='sigmoid')(x)

model = Model(inputs=base_model.input, outputs=predictions)
model.compile(optimizer=Adam(learning_rate=1e-4), loss='binary_crossentropy', metrics=['accuracy', 'AUC'])


# Sauvegarder le meilleur modèle (basé sur val_accuracy)
checkpoint = ModelCheckpoint(
    filepath='best_model.h5',
    monitor='val_accuracy',
    save_best_only=True,
    save_weights_only=False,
    verbose=1
)
# Arrêter si pas d'amélioration pendant 5 epochs
early_stop = EarlyStopping(
    monitor='val_loss',
    patience=5,
    restore_best_weights=True,
    verbose=1
)

# Réduire le learning rate si plateau de validation
lr_scheduler = ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.5,
    patience=3,
    min_lr=1e-6,
    verbose=1
)

# Regrouper les callbacks
callbacks = [checkpoint, early_stop, lr_scheduler]

history = model.fit(
    train_gen,
    validation_data=val_gen,
    epochs=10,
    verbose=1
    
)

# Charger EfficientNetB4 sans la dernière couche (include_top=False)
base_model1 = EfficientNetB4(
    weights='imagenet',
    include_top=False,
    input_shape=(224, 224, 3)  # ou (380, 380, 3) si tu veux la taille native
)

# Geler les couches du modèle pré-entraîné
base_model1.trainable = False

# Ajouter des couches personnalisées pour la classification binaire
x = base_model1.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.4)(x)
output = Dense(1, activation='sigmoid')(x)

model = Model(inputs=base_model1.input, outputs=output)

# Compiler
model.compile(optimizer=Adam(learning_rate=1e-4),
              loss='binary_crossentropy',
              metrics=['accuracy', 'AUC'])

history = model.fit(
    train_gen,
    validation_data=val_gen,
    epochs=30,  # tu peux augmenter ici
    callbacks=callbacks,
    verbose=1
)


# Chargement d’EfficientNetB1 sans la tête de classification
base_model2 = EfficientNetB1(
    weights='imagenet',
    include_top=False,
    input_shape=(240, 240, 3)  # Taille recommandée pour EfficientNetB1
)

# Geler les poids pour le feature extraction
base_model2.trainable = False

# Construction de la "tête"
x = base_model2.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.4)(x)
output = Dense(1, activation='sigmoid')(x)

# Création du modèle final
model = Model(inputs=base_model2.input, outputs=output)

# Compilation
model.compile(
    optimizer=Adam(learning_rate=1e-4),
    loss='binary_crossentropy',
    metrics=['accuracy', 'AUC']
)


history = model.fit(
    train_gen,
    validation_data=val_gen,
    epochs=25,
    callbacks=callbacks,
    verbose=1
)