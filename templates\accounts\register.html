{% extends 'base.html' %}

{% block title %}PulmoScan - Inscription{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-7">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header bg-primary text-white text-center">
                <h3 class="my-2"><i class="fas fa-user-plus me-2"></i> Inscription</h3>
            </div>
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">Prénom</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {{ form.first_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Nom</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {{ form.last_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">Nom d'utilisateur</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger">
                                {{ form.username.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ form.username.help_text }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {{ form.email.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password1.id_for_label }}" class="form-label">Mot de passe</label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                            <div class="text-danger">
                                {{ form.password1.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ form.password1.help_text }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password2.id_for_label }}" class="form-label">Confirmer le mot de passe</label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                            <div class="text-danger">
                                {{ form.password2.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ form.password2.help_text }}</div>
                    </div>
                    
                    <div class="mt-4 mb-0">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-block">S'inscrire</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{% url 'login' %}" class="text-decoration-none">Déjà un compte? Se connecter</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
