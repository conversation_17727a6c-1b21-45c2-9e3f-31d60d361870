{% extends 'base.html' %}

{% block title %}PulmoScan - Connexion{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header bg-primary text-white text-center">
                <h3 class="my-2"><i class="fas fa-sign-in-alt me-2"></i> Connexion</h3>
            </div>
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">Nom d'utilisateur</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger">
                                {{ form.username.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">Mot de passe</label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger">
                                {{ form.password.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex align-items-center justify-content-between mt-4 mb-0">
                        <a class="small text-decoration-none" href="#">Mot de passe oublié?</a>
                        <button type="submit" class="btn btn-primary">Se connecter</button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{% url 'register' %}" class="text-decoration-none">Pas encore de compte? S'inscrire!</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
